---
name: Maintenance/Refactor
about: Track technical debt, refactoring, or code health work
title: "[MAINT]: "
labels: maintenance, triage
assignees: ''

---

## Location/Component
Which part of the codebase does this affect? (e.g., `src/utils/helpers.js`, `UserService class`)

## Current Issue
Describe the current state that needs improvement. (e.g., "This file is 2000 lines long and is difficult to test," "We're using a deprecated library," "This logic is duplicated in three places.")

## Proposed Improvement
Describe the desired outcome and any proposed solutions.

## Additional Context
Link to related issues, add code snippets, or describe the benefits of doing this work (e.g., improved performance, better testability).
