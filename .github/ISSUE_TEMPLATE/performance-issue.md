---
name: Performance Issue
about: Report a performance regression or suggest an optimization
title: "[PERF]: "
labels: performance, triage
assignees: ''

---

## What area is related to the performance issue?
e.g., API response time, page load, rendering, a specific query, etc.

## How are you measuring performance?
e.g., Lighthouse score, WebPageTest, a specific benchmark, network tab timing.

## Provide metrics (if possible)
Provide specific numbers before and after, if you have them.
- **Current:** e.g., 2.5s load time
- **Expected:** e.g., <1s load time

## Steps to reproduce (if applicable)
Steps to observe the performance issue.

## Additional context
Add any other context about the problem here.
