<script setup lang="ts">
const model = defineModel<object[] | string[]>()
const {icon, label, defaultValueGetter, raw = false} = defineProps<{
	icon?: string
	label?: string
	defaultValueGetter: () => object | string
	raw?: boolean
}>()
</script>

<template>
	<UButton
		v-if="raw"
		:icon
		:label
		size="md"
		variant="soft"
		class="mx-auto cursor-pointer"
		@click="() => { model!.push(defaultValueGetter()) }"/>
	<div v-else class="flex">
		<UButton
			:icon
			:label
			size="md"
			variant="soft"
			class="mx-auto cursor-pointer"
			@click="() => { model!.push(defaultValueGetter()) }"/>
	</div>
</template>

<style scoped>

</style>