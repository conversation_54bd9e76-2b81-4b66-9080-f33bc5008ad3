<script setup lang="ts">
const technologies = [
	{label: "Alpine Linux", value: "alpine-linux", icon: "i-simple-icons-alpinelinux"},
	{label: "Alpine.js", value: "alpinejs", icon: "i-simple-icons-alpinedotjs"},
	{label: "Android", value: "android", icon: "i-simple-icons-android"},
	{label: "Angular", value: "angular", icon: "i-simple-icons-angular"},
	{label: "Ansible", value: "ansible", icon: "i-simple-icons-ansible"},
	{label: "Ant Design", value: "antd", icon: "i-simple-icons-antdesign"},
	{label: "Apache", value: "apache", icon: "i-simple-icons-apache"},
	{label: "Apollo", value: "apollo", icon: "i-simple-icons-apollographql"},
	{label: "Arch Linux", value: "arch-linux", icon: "i-simple-icons-archlinux"},
	{label: "Astro", value: "astro", icon: "i-simple-icons-astro"},
	{label: "AWS", value: "aws", icon: "i-simple-icons-amazonaws"},
	{label: "Azure", value: "azure", icon: "i-simple-icons-microsoftazure"},
	{label: "Babel", value: "babel", icon: "i-simple-icons-babel"},
	{label: "Bash", value: "bash", icon: "i-simple-icons-gnubash"},
	{label: "Bitbucket", value: "bitbucket", icon: "i-simple-icons-bitbucket"},
	{label: "Bootstrap", value: "bootstrap", icon: "i-simple-icons-bootstrap"},
	{label: "Bulma", value: "bulma", icon: "i-simple-icons-bulma"},
	{label: "Bun", value: "bun", icon: "i-simple-icons-bun"},
	{label: "C", value: "c", icon: "i-simple-icons-c"},
	{label: "C#", value: "csharp", icon: "i-simple-icons-csharp"},
	{label: "C++", value: "cpp", icon: "i-simple-icons-cplusplus"},
	{label: "Cassandra", value: "cassandra", icon: "i-simple-icons-apachecassandra"},
	{label: "CentOS", value: "centos", icon: "i-simple-icons-centos"},
	{label: "Chakra UI", value: "chakra-ui", icon: "i-simple-icons-chakraui"},
	{label: "CircleCI", value: "circleci", icon: "i-simple-icons-circleci"},
	{label: "ClickHouse", value: "clickhouse", icon: "i-simple-icons-clickhouse"},
	{label: "Cloudflare", value: "cloudflare", icon: "i-simple-icons-cloudflare"},
	{label: "CockroachDB", value: "cockroachdb", icon: "i-simple-icons-cockroachlabs"},
	{label: "Confluence", value: "confluence", icon: "i-simple-icons-confluence"},
	{label: "CSS", value: "css", icon: "i-simple-icons-css3"},
	{label: "Cypress", value: "cypress", icon: "i-simple-icons-cypress"},
	{label: "D3.js", value: "d3", icon: "i-simple-icons-d3dotjs"},
	{label: "DaisyUI", value: "daisyui", icon: "i-simple-icons-daisyui"},
	{label: "Debian", value: "debian", icon: "i-simple-icons-debian"},
	{label: "Deno", value: "deno", icon: "i-simple-icons-deno"},
	{label: "Discord", value: "discord", icon: "i-simple-icons-discord"},
	{label: "Discord.js", value: "discordjs", icon: "i-devicon-plain-discordjs"},
	{label: "Django", value: "django", icon: "i-simple-icons-django"},
	{label: "Docker", value: "docker", icon: "i-simple-icons-docker"},
	{label: "Drupal", value: "drupal", icon: "i-simple-icons-drupal"},
	{label: "Elastic", value: "elastic", icon: "i-simple-icons-elastic"},
	{label: "Electron", value: "electron", icon: "i-simple-icons-electron"},
	{label: "Element UI", value: "element-ui", icon: "i-simple-icons-element"},
	{label: "Ember.js", value: "emberjs", icon: "i-simple-icons-emberdotjs"},
	{label: "Elixir", value: "elixir", icon: "i-simple-icons-elixir"},
	{label: "Ember.js", value: "ember", icon: "i-simple-icons-emberdotjs"},
	{label: "ESLint", value: "eslint", icon: "i-simple-icons-eslint"},
	{label: "Express", value: "express", icon: "i-simple-icons-express"},
	{label: "FastAPI", value: "fastapi", icon: "i-simple-icons-fastapi"},
	{label: "Fedora", value: "fedora", icon: "i-simple-icons-fedora"},
	{label: "Firebase", value: "firebase", icon: "i-simple-icons-firebase"},
	{label: "Flask", value: "flask", icon: "i-simple-icons-flask"},
	{label: "Flutter", value: "flutter", icon: "i-simple-icons-flutter"},
	{label: "Forgejo", value: "forgejo", icon: "i-simple-icons-forgejo"},
	{label: "Git", value: "git", icon: "i-simple-icons-git"},
	{label: "Gitea", value: "gitea", icon: "i-simple-icons-gitea"},
	{label: "GitHub", value: "github", icon: "i-simple-icons-github"},
	{label: "GitHub Actions", value: "github-actions", icon: "i-simple-icons-githubactions"},
	{label: "GitLab", value: "gitlab", icon: "i-simple-icons-gitlab"},
	{label: "Go", value: "go", icon: "i-simple-icons-go"},
	{label: "Gradle", value: "gradle", icon: "i-simple-icons-gradle"},
	{label: "Grafana", value: "grafana", icon: "i-simple-icons-grafana"},
	{label: "GraphQL", value: "graphql", icon: "i-simple-icons-graphql"},
	{label: "Gulp", value: "gulp", icon: "i-simple-icons-gulp"},
	{label: "Hadoop", value: "hadoop", icon: "i-simple-icons-apachehadoop"},
	{label: "Haskell", value: "haskell", icon: "i-simple-icons-haskell"},
	{label: "Hasura", value: "hasura", icon: "i-simple-icons-hasura"},
	{label: "Heroku", value: "heroku", icon: "i-simple-icons-heroku"},
	{label: "HTML", value: "html", icon: "i-simple-icons-html5"},
	{label: "IntelliJ IDEA", value: "intellij", icon: "i-simple-icons-intellijidea"},
	{label: "iOS", value: "ios", icon: "i-simple-icons-ios"},
	{label: "Java", value: "java", icon: "i-lineicons-java"},
	{label: "JavaScript", value: "javascript", icon: "i-simple-icons-javascript"},
	{label: "Jenkins", value: "jenkins", icon: "i-simple-icons-jenkins"},
	{label: "Jest", value: "jest", icon: "i-simple-icons-jest"},
	{label: "Jira", value: "jira", icon: "i-simple-icons-jira"},
	{label: "jQuery", value: "jquery", icon: "i-simple-icons-jquery"},
	{label: "Julia", value: "julia", icon: "i-simple-icons-julia"},
	{label: "Kafka", value: "kafka", icon: "i-simple-icons-apachekafka"},
	{label: "Kali Linux", value: "kali-linux", icon: "i-simple-icons-kalilinux"},
	{label: "Kotlin", value: "kotlin", icon: "i-simple-icons-kotlin"},
	{label: "Kubernetes", value: "kubernetes", icon: "i-simple-icons-kubernetes"},
	{label: "Laravel", value: "laravel", icon: "i-simple-icons-laravel"},
	{label: "Less", value: "less", icon: "i-simple-icons-less"},
	{label: "Linux", value: "linux", icon: "i-simple-icons-linux"},
	{label: "Lit", value: "lit", icon: "i-simple-icons-lit"},
	{label: "Lua", value: "lua", icon: "i-simple-icons-lua"},
	{label: "MariaDB", value: "mariadb", icon: "i-simple-icons-mariadb"},
	{label: "Material UI", value: "material-ui", icon: "i-simple-icons-mui"},
	{label: "Mercurial", value: "mercurial", icon: "i-simple-icons-mercurial"},
	{label: "Microsoft Teams", value: "microsoft-teams", icon: "i-simple-icons-microsoftteams"},
	{label: "MongoDB", value: "mongodb", icon: "i-simple-icons-mongodb"},
	{label: "MySQL", value: "mysql", icon: "i-simple-icons-mysql"},
	{label: "NestJS", value: "nestjs", icon: "i-simple-icons-nestjs"},
	{label: "Netlify", value: "netlify", icon: "i-simple-icons-netlify"},
	{label: "Next.js", value: "nextjs", icon: "i-simple-icons-nextdotjs"},
	{label: "Nginx", value: "nginx", icon: "i-simple-icons-nginx"},
	{label: "Node.js", value: "nodejs", icon: "i-simple-icons-nodedotjs"},
	{label: "npm", value: "npm", icon: "i-simple-icons-npm"},
	{label: "Nuxt", value: "nuxt", icon: "i-simple-icons-nuxtdotjs"},
	{label: "OpenSUSE", value: "opensuse", icon: "i-simple-icons-opensuse"},
	{label: "Oracle", value: "oracle", icon: "i-simple-icons-oracle"},
	{label: "PHP", value: "php", icon: "i-simple-icons-php"},
	{label: "Playwright", value: "playwright", icon: "i-simple-icons-playwright"},
	{label: "pnpm", value: "pnpm", icon: "i-simple-icons-pnpm"},
	{label: "PostCSS", value: "postcss", icon: "i-simple-icons-postcss"},
	{label: "PostgreSQL", value: "postgresql", icon: "i-simple-icons-postgresql"},
	{label: "Preact", value: "preact", icon: "i-simple-icons-preact"},
	{label: "Prettier", value: "prettier", icon: "i-simple-icons-prettier"},
	{label: "Prisma", value: "prisma", icon: "i-simple-icons-prisma"},
	{label: "Python", value: "python", icon: "i-simple-icons-python"},
	{label: "Qwik", value: "qwik", icon: "i-simple-icons-qwik"},
	{label: "RabbitMQ", value: "rabbitmq", icon: "i-simple-icons-rabbitmq"},
	{label: "React", value: "react", icon: "i-simple-icons-react"},
	{label: "Redis", value: "redis", icon: "i-simple-icons-redis"},
	{label: "RedHat", value: "redhat", icon: "i-simple-icons-redhat"},
	{label: "Remix", value: "remix", icon: "i-simple-icons-remix"},
	{label: "Rollup", value: "rollup", icon: "i-simple-icons-rollupdotjs"},
	{label: "Ruby", value: "ruby", icon: "i-simple-icons-ruby"},
	{label: "Ruby on Rails", value: "ruby-on-rails", icon: "i-simple-icons-rubyonrails"},
	{label: "Rust", value: "rust", icon: "i-simple-icons-rust"},
	{label: "Sass", value: "sass", icon: "i-simple-icons-sass"},
	{label: "Scala", value: "scala", icon: "i-simple-icons-scala"},
	{label: "Selenium", value: "selenium", icon: "i-simple-icons-selenium"},
	{label: "Sequelize", value: "sequelize", icon: "i-devicon-plain-sequelize"},
	{label: "Slack", value: "slack", icon: "i-simple-icons-slack"},
	{label: "Solid.js", value: "solidjs", icon: "i-simple-icons-solid"},
	{label: "Sourcehut", value: "sourcehut", icon: "i-simple-icons-sourcehut"},
	{label: "Spotify", value: "spotify", icon: "i-simple-icons-spotify"},
	{label: "Spring", value: "spring", icon: "i-simple-icons-spring"},
	{label: "SQLite", value: "sqlite", icon: "i-simple-icons-sqlite"},
	{label: "Strapi", value: "strapi", icon: "i-simple-icons-strapi"},
	{label: "Stylus", value: "stylus", icon: "i-simple-icons-stylus"},
	{label: "Supabase", value: "supabase", icon: "i-simple-icons-supabase"},
	{label: "Subversion", value: "subversion", icon: "i-simple-icons-subversion"},
	{label: "Svelte", value: "svelte", icon: "i-simple-icons-svelte"},
	{label: "Swift", value: "swift", icon: "i-simple-icons-swift"},
	{label: "Symfony", value: "symfony", icon: "i-simple-icons-symfony"},
	{label: "Tailwind", value: "tailwind", icon: "i-simple-icons-tailwindcss"},
	{label: "Terraform", value: "terraform", icon: "i-simple-icons-terraform"},
	{label: "TypeScript", value: "typescript", icon: "i-simple-icons-typescript"},
	{label: "Ubuntu", value: "ubuntu", icon: "i-simple-icons-ubuntu"},
	{label: "Unity", value: "unity", icon: "i-simple-icons-unity"},
	{label: "Unreal Engine", value: "unreal-engine", icon: "i-simple-icons-unrealengine"},
	{label: "Vercel", value: "vercel", icon: "i-simple-icons-vercel"},
	{label: "Vim", value: "vim", icon: "i-simple-icons-vim"},
	{label: "Vite", value: "vite", icon: "i-simple-icons-vite"},
	{label: "VS Code", value: "vs-code", icon: "i-simple-icons-visualstudiocode"},
	{label: "Vue", value: "vue", icon: "i-simple-icons-vuedotjs"},
	{label: "Vuetify", value: "vuetify", icon: "i-simple-icons-vuetify"},
	{label: "WebStorm", value: "webstorm", icon: "i-simple-icons-webstorm"},
	{label: "Webpack", value: "webpack", icon: "i-simple-icons-webpack"},
	{label: "Windows", value: "windows", icon: "i-simple-icons-windows"},
	{label: "Windicss", value: "windicss", icon: "i-simple-icons-windicss"},
	{label: "Yarn", value: "yarn", icon: "i-simple-icons-yarn"},
	{label: "Zoom", value: "zoom", icon: "i-simple-icons-zoom"}
].sort((a, b) => a.label.localeCompare(b.label))

const {gitPlatformsOnly, single} = defineProps<{
	gitPlatformsOnly?: boolean
	single?: boolean
}>()

const filteredTechnologies = computed(() => {
	if (gitPlatformsOnly) {
		return technologies.filter(tech => [
			'bitbucket',
			'forgejo',
			'gerrit',
			'git',
			'gitea',
			'github',
			'gitlab',
			'mercurial',
			'sourcehut',
			'subversion'
		].includes(tech.value))
	}
	return technologies
})
</script>

<template>
	<USelectMenu
		class="min-w-40"
		:multiple="!single"
		placeholder="Choose Technologies"
		variant="soft"
		:items="filteredTechnologies"/>
</template>

<style scoped>

</style>