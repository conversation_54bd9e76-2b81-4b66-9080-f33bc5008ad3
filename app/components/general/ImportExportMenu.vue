<script setup lang="ts">
import J<PERSON>Z<PERSON> from "jszip"

const downloadZip = () => {
	const resumeData = useResumeData()
	const resumeStyle = useResumeStyle()
	const zip = new JSZip()

	zip.file("resume-data.json", JSON.stringify(resumeData.data, null, 2))
	if (resumeData.avatar)
		zip.file("resume-avatar.webp", resumeData.avatar)
	zip.file("resume-style.json", JSON.stringify({
		font: resumeStyle.value.font,
		colors: resumeStyle.value.colors,
		effects: resumeStyle.value.effects,
		layout: resumeStyle.value.layout,
		sections: resumeStyle.value.sections,
	}, null, 2))
	zip.generateAsync({type: "blob"}).then((blob) => {
		const url = URL.createObjectURL(blob)
		const a = document.createElement("a")
		a.href = url
		a.download = `resume-export-${new Date().toISOString()}.zip`
		a.click()
		document.body.removeChild(a)
		URL.revokeObjectURL(url)
	})
}
</script>

<template>
	<UFieldGroup>
		<GeneralResumeLoader/>
		<UButton
			class="cursor-pointer"
			label="Export"
			icon="i-material-symbols-file-export-rounded"
			variant="outline"
			loading-auto
			@click="downloadZip"
		/>
	</UFieldGroup>
</template>

<style scoped>

</style>