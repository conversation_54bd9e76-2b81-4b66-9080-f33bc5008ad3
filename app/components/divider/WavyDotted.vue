<script setup lang="ts">
const style = useResumeStyle()
</script>

<template>
	<svg
			preserveAspectRatio='xMaxYMid slice'
			xmlns='http://www.w3.org/2000/svg'
			viewBox='0 0 91.5 2000'>
		<path
				:fill='style.colors.bgElevated'
				d='M0 0h26c-1 14 4 29 11 42 12 21 30 40 34 63 5 24-6 48-16 71-8 21-16 42-18 64-5 47 17 94 33 140s25 98-4 139c-8 13-20 25-19 39 1 10 7 18 12 26 22 34 28 75 15 112-4 12-11 25-24 31-18 8-26 28-23 45s15 30 24 46c28 51 23 111 18 167l-2 15 2 15c5 56 10 116-18 168-9 15-20 29-24 45s5 37 23 45c13 6 20 19 24 31 13 37 7 78-15 112-5 8-11 16-12 26-1 14 11 26 19 39 29 41 20 93 4 139s-38 93-33 140c2 22 10 43 18 64 10 23 21 47 16 71-4 23-22 42-34 63-7 13-12 28-11 42H0z'
				opacity='.75'/>
		<path
				:fill='style.colors.bgElevated'
				d='M0 1458c9 21 18 48 29 80a127 127 0 015 65l-1 1-7 32-4 19a78 78 0 00-1 19 87 87 0 004 24 180 180 0 0011 25l3 6 1 2 4 7 1 2 9 21c7 16 7 31 4 46-2 15-8 31-15 46l-8 19c-7 19-13 38-12 58v3a71 71 0 0010 32 97 97 0 0016 19 283 283 0 0018 16H32c-11-18-19-38-22-57-4-31 5-69 14-99l3-10a70 70 0 015-10c8-17 15-36 6-54l-1-1a199 199 0 00-11-18l-7-11a76 76 0 01-5-11 87 87 0 01-5-18 124 124 0 01-1-19 265 265 0 012-32c2-21 7-43 5-62-2-17-8-32-15-47zM0 815l1 2 11 15 18 24a424 424 0 0135 65 331 331 0 0112 31q4 10 6 21a132 132 0 014 27 132 132 0 01-4 27q-2 11-6 21a331 331 0 01-12 31 424 424 0 01-35 65l-18 24-11 15-1 2v-8l6-9c30-41 29-116 3-168 26-52 27-127-3-168l-6-9z'
				opacity='.51'/>
		<path
				:fill='style.colors.bgElevated'
				d='M0 1304c13 17 28 33 39 50 10 16 16 33 13 54a251 251 0 01-6 28c-8 29-17 56 0 83l5 11c21 45 28 111-2 178q-4 11-10 21l-13 22a307 307 0 01-26 33v-82a403 403 0 0021-28 238 238 0 0019-35 138 138 0 006-14 140 140 0 004-17 91 91 0 001-32 103 103 0 00-7-24c-14-31-36-56-21-115 2-11 8-19 12-30l1-4c2-10 0-19-5-27-6-12-17-23-23-32l-8-15z'
				opacity='.51'/>
		<circle
				:fill='style.colors.bgElevated' cx='72.6' cy='1940.8' r='17.1' transform='rotate(-67 73 1941)'
				opacity='.51'/>
		<path :fill='style.colors.bgElevated' d='M0 1420a19 19 0 019 6 19 19 0 01-5 26 18 18 0 01-4 2z'/>
		<circle :fill='style.colors.bgElevated' cx='30' cy='1710.8' r='13.4'/>
		<circle :fill='style.colors.bgElevated' cx='62.6' cy='1741.9' r='5.9' opacity='.51'/>
		<circle :fill='style.colors.bgElevated' cx='63.8' cy='1060.7' r='18.6' opacity='.51'/>
		<circle :fill='style.colors.bgElevated' cx='34.4' cy='1320.5' r='13.4'/>
		<circle :fill='style.colors.bgElevated' cx='82.3' cy='1109' r='8.9' opacity='.51'/>
		<circle
				:fill='style.colors.bgElevated' cx='82.7' cy='1625.5' r='8.9' transform='rotate(-45 83 1625)'
				opacity='.51'/>
		<circle :fill='style.colors.bgElevated' cx='84.7' cy='1887.5' r='6.1' opacity='.51'/>
		<path
				:fill='style.colors.bgElevated'
				d='M0 1166h1a80 80 0 0142 27 80 80 0 0118 41 80 80 0 01-2 29 80 80 0 01-59 58v1z' opacity='.51'/>
		<path
				:fill='style.colors.bgElevated'
				d='M0 0h44a63 63 0 015 15v1c2 16 0 33-3 50l-11 62a363 363 0 00-3 48v20a323 323 0 005 35l7 31 5 30a195 195 0 01-3 83l-5 22a96 96 0 003 51l7 22c5 14 10 29 9 44 0 17-7 34-14 50-4 9-9 19-11 29a72 72 0 00-3 34 84 84 0 007 19l8 18a128 128 0 012 96l-2 4c-4 11-10 21-13 32l-2 4c-5 18-4 37-2 56v1l5 77a121 121 0 01-9 66l2 4c9 19 8 41 7 62l-5 77v1a166 166 0 004 60c3 11 9 21 13 32l2 4a128 128 0 01-2 96l-8 18a84 84 0 00-7 19l-1 3c-1 10 0 21 4 31 2 10 7 19 11 29 7 16 14 33 14 50 1 15-4 30-9 44l-7 22a96 96 0 00-3 51l5 22a443 443 0 015 24c3 20 1 40-2 59l-5 30-7 31a323 323 0 00-5 35v20a363 363 0 003 48l11 62c3 17 6 34 3 50v1a63 63 0 01-5 15H0z'/>
		<path
				:fill='style.colors.bgElevated'
				d='M0 542c9-21 18-48 29-80a127 127 0 005-65l-1-1-7-32-4-19a78 78 0 01-1-19 87 87 0 014-24 180 180 0 0111-25l3-6 1-2 4-7 1-2 9-21c7-16 7-31 4-46-2-15-8-31-15-46l-8-19c-7-19-13-38-12-58v-3a71 71 0 0110-32 97 97 0 0116-19A283 283 0 0167 0H32C21 18 13 38 10 57c-4 31 5 69 14 99l3 10a70 70 0 005 10c8 17 15 36 6 54l-1 1a199 199 0 01-11 18l-7 11a76 76 0 00-5 11 87 87 0 00-5 18 124 124 0 00-1 19 265 265 0 002 32c2 21 7 43 5 62-2 17-8 32-15 47z'
				opacity='.51'/>
		<path
				:fill='style.colors.bgElevated'
				d='M0 696c13-17 28-33 39-50 10-16 16-33 13-54a251 251 0 00-6-28c-8-29-17-56 0-83l5-11c21-45 28-111-2-178q-4-11-10-21l-13-22a307 307 0 00-26-33v82a403 403 0 0121 28 238 238 0 0119 35 138 138 0 016 14 140 140 0 014 17 91 91 0 011 32 103 103 0 01-7 24c-14 31-36 56-21 115 2 11 8 19 12 30l1 4c2 10 0 19-5 27-6 12-17 23-23 32l-8 15z'
				opacity='.51'/>
		<circle :fill='style.colors.bgElevated' cx='72.6' cy='59.2' r='17.1' transform='rotate(-23 73 59)' opacity='.51'/>
		<path :fill='style.colors.bgElevated' d='M0 580a19 19 0 009-6 19 19 0 00-5-26 18 18 0 00-4-2z'/>
		<circle :fill='style.colors.bgElevated' cx='30' cy='289.2' r='13.4' transform='rotate(-2 30 289)'/>
		<circle :fill='style.colors.bgElevated' cx='62.6' cy='258.1' r='5.9' opacity='.51'/>
		<circle :fill='style.colors.bgElevated' cx='63.8' cy='939.3' r='18.6' opacity='.51'/>
		<circle :fill='style.colors.bgElevated' cx='34.4' cy='679.5' r='13.4'/>
		<circle :fill='style.colors.bgElevated' cx='82.3' cy='891' r='8.9' opacity='.51'/>
		<circle :fill='style.colors.bgElevated' cx='82.7' cy='374.5' r='8.9' transform='rotate(-45 83 375)' opacity='.51'/>
		<circle :fill='style.colors.bgElevated' cx='84.7' cy='112.5' r='6.1' opacity='.51'/>
		<path
				:fill='style.colors.bgElevated'
				d='M0 834h1a80 80 0 0042-27 80 80 0 0018-41 80 80 0 00-2-29 80 80 0 00-59-58v-1z' opacity='.51'/>
	</svg>
</template>

<style scoped>

</style>