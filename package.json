{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"dev": "nuxt dev", "dev:host": "nuxt dev --host", "build": "nuxt build", "build:analyze": "nuxt build --analyze", "preview": "nuxt preview", "start": "node .output/server/index.mjs", "serve": "pnpm run build && pnpm run start", "generate": "nuxt generate", "generate:modern": "nuxt generate --modern", "clean": "rm -rf .nuxt .output dist .cache", "clean:all": "npm run clean && rm -rf node_modules", "postinstall": "nuxt prepare", "update-deps": "pnpm update", "lint": "eslint .", "lint:fix": "eslint --fix .", "typecheck": "nuxt typecheck", "prod:build": "pnpm run clean && pnpm run build", "deploy": "pnpm run clean && pnpm run build && pnpm run start"}, "dependencies": {"@iconify-json/lucide": "^1.2.64", "@iconify-json/simple-icons": "^1.2.49", "@internationalized/date": "^3.9.0", "@nuxt/image": "1.10.0", "@nuxt/ui": "4.0.0-alpha.0", "jszip": "^3.10.1", "nuxt": "^4.0.3", "sharp": "^0.34.3", "uuid": "^11.1.0", "valibot": "^1.1.0", "vue-to-print": "^1.4.0"}, "devDependencies": {"@nuxt/eslint": "^1.9.0", "@nuxtjs/supabase": "^1.6.1", "@types/uuid": "^10.0.0", "eslint": "^9.34.0", "nuxt-particles": "https://github.com/LeeKrane/nuxt-particles.git#main", "typescript": "^5.9.2"}}